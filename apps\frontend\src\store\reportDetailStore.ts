import { create } from "zustand";
import { REPORT_SECTIONS } from "~/helpers/constants";
import { ReportMetadata, ReportSection } from "~/types/global";
import { produce } from "immer";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";
import { useCallback, useEffect } from "react";

export type SectionListRecordsItem = {
  title: string;
  data_count: number;
};

type UpdateFnPromise = (entry: any, index?: number) => void;

let globalForceSave: (() => void) | null = null;

interface ReportDetailStoreActions {
  setReportSections: (sections: ReportSection[]) => void;
  setDeletedSections: (sections: ReportSection[]) => void;
  setReportType: (type: string) => void;
  setMetadata: (metadata: ReportMetadata) => void;
  setProfileImage: (image: string) => void;
  resetReportDetailStore: () => void;
  updateSectionEntries: (
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number
  ) => void;
  scheduleAutoSave: () => void;
  cancelAutoSave: () => void;
  forceSave: () => void;
  isPendingSave: () => boolean;
  hasPendingChanges: () => boolean;
}

interface ReportDetailStoreState {
  sections: ReportSection[];
  deletedSections: ReportSection[];
  metadata: ReportMetadata | null;
  sectionListRecords: SectionListRecordsItem[];
  totalRecords: number;
  reportType: string;
  profileImage: string | null;
  actions: ReportDetailStoreActions;
  _autoSave: {
    timeoutId: NodeJS.Timeout | null;
    isPending: boolean;
    hasPendingChanges: boolean;
    mutation: any;
  };
}

const initialState = {
  sections: [] as ReportSection[],
  deletedSections: [] as ReportSection[],
  metadata: null as ReportMetadata | null,
  sectionListRecords: [] as SectionListRecordsItem[],
  totalRecords: 0,
  reportType: "",
  profileImage: null as string | null,
  _autoSave: {
    timeoutId: null as NodeJS.Timeout | null,
    isPending: false,
    hasPendingChanges: false,
    mutation: null as any,
  },
};

const useReportDetailStore = create<ReportDetailStoreState>((set, get) => ({
  ...initialState,
  actions: {
    resetReportDetailStore: () => set(() => initialState),
    setReportSections: (sections) => {
      const imagensSection = sections.find(
        (section) => section.title === REPORT_SECTIONS.imagens
      );
      if (imagensSection) {
        const firstImage = imagensSection.data[0].detalhes.find(
          (d: any) => d.value.url
        );
        if (firstImage) {
          set({ profileImage: firstImage.value.url.value });
        }
      } else {
        set({ profileImage: null });
      }

      const sectionListRecords = sections
        .filter(section => {
          if (section.subsection) return false;
          if (section.is_deleted) return false;
          if (section.data_count > 0) return true;

          // Algumas seções vazias vem com a chave "detalhes" vazia dentro de "data", por isso a verificação abaixo
          return Array.isArray(section.data) &&
            section.data.some((item) =>
              Array.isArray((item as any).detalhes) &&
              (item as any).detalhes.length > 0
            );
        })
        .map((s) => ({ title: s.title, data_count: s.data_count }));

      const totalRecords = sectionListRecords.reduce((sum, section) => {
        const count = Number(section.data_count) || 0;
        return sum + count;
      }, 0);

      set({ sections });
      set({ sectionListRecords });
      set({ totalRecords });
    },

    setDeletedSections: (deletedSections) => set({ deletedSections }),

    setMetadata: (metadata) => set({ metadata }),

    setReportType: (reportType) => set({ reportType }),

    setProfileImage: (profileImage) => set({ profileImage }),

    updateSectionEntries: (
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    ) =>
      set((state) =>
        produce(state, (draft) => {
          const section = draft.sections.find(
            (s) => s.title === sectionTitle
          );
          if (!section) return;

          // 1) apply the mutation you passed, *with* its index
          section.data.forEach((entry, i) => updaterFn(entry as any, i))

          // 2) set the section flag however *you* want
          section.is_deleted = testSectionDeletedFn(section);

          if (calculateDataCountFn) {
            section.data_count = calculateDataCountFn(section);
          }
        })
      ),

    scheduleAutoSave: () => {
      const state = get();
      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          hasPendingChanges: true,
        }
      }));

      if (state._autoSave.isPending && state._autoSave.mutation) {
        state._autoSave.mutation.reset();
        set((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
          }
        }));
      }

      if (state._autoSave.timeoutId) {
        clearTimeout(state._autoSave.timeoutId);
      }

      const timeoutId = setTimeout(() => {
        if (globalForceSave) {
          globalForceSave();
        }
      }, 5000);

      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          timeoutId,
        }
      }));
    },

    cancelAutoSave: () => {
      const state = get();
      if (state._autoSave.timeoutId) {
        clearTimeout(state._autoSave.timeoutId);
      }
      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          timeoutId: null,
          hasPendingChanges: false,
        }
      }));
    },

    forceSave: () => {
      // This will be set by useReportActionsWithAutoSave when it's initialized
      console.warn("forceSave not yet initialized");
    },

    isPendingSave: () => get()._autoSave.isPending,

    hasPendingChanges: () => get()._autoSave.hasPendingChanges,
  },
}));

export const useReportSections = () =>
  useReportDetailStore((state) => state.sections);
export const useReportDeletedSections = () =>
  useReportDetailStore((state) => state.deletedSections);
export const useReportMetadata = () =>
  useReportDetailStore((state) => state.metadata);
export const useReportType = () =>
  useReportDetailStore((state) => state.reportType);
export const useReportProfileImage = () =>
  useReportDetailStore((state) => state.profileImage);
export const useReportDetailActions = () =>
  useReportDetailStore((state) => state.actions);
export const useReportSectionListRecords = () =>
  useReportDetailStore((state) => state.sectionListRecords);
export const useTotalRecords = () =>
  useReportDetailStore((state) => state.totalRecords);

export function useReportActionsWithAutoSave() {
  const actions = useReportDetailStore((state) => state.actions);
  const { addNewReportMutation,
    invalidateReportDetails,
    setReportDetailsToStale,
    resetReportListQuery
  } = useReportCRUD();

  const executeSave = useCallback(() => {
    const state = useReportDetailStore.getState();

    if (!state._autoSave.hasPendingChanges || state._autoSave.isPending) {
      return;
    }

    const { sections, metadata } = state;
    const reportId = metadata?.user_reports_id as string;

    const payload = {
      ...metadata,
      data: { [metadata?.report_type as string]: sections },
    };

    console.log("[useReportActionsWithAutoSave] Executando save com estado final:", payload);

    useReportDetailStore.setState((state) => ({
      ...state,
      _autoSave: {
        ...state._autoSave,
        isPending: true,
        hasPendingChanges: false,
        mutation: addNewReportMutation,
      }
    }));

    addNewReportMutation.mutate(payload, {
      onSuccess: () => {
        useReportDetailStore.setState((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
          }
        }));
        toast.success("Alterações salvas com sucesso!");
        if (reportId) {
          setReportDetailsToStale(reportId)  // marca como stale para refetch quando sair e entrar nos detalhes de novo
          resetReportListQuery() // TODO - por quanto estou fazendo refetch da lista para mostrar o card do report modificado primeiro - REVISAR
        };
      },
      onError: (error) => {
        useReportDetailStore.setState((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
            hasPendingChanges: true,
          }
        }));
        console.error("[AutoSave] Erro ao salvar:", error);
        toast.error("Falha ao salvar, tente novamente mais tarde.");
        if (reportId) invalidateReportDetails(reportId); // aqui é necessário invalidar para forçar o refetch
      },
    });
  }, [addNewReportMutation, invalidateReportDetails]);

  // Set the global reference on first render
  useEffect(() => {
    globalForceSave = executeSave;
    return () => {
      globalForceSave = null;
    };
  }, [executeSave]);

  const updateSectionEntries = useCallback((
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number
  ) => {
    actions.updateSectionEntries(
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    );

    actions.scheduleAutoSave();
  }, [actions]);

  return {
    ...actions,
    updateSectionEntries,
    forceSave: executeSave,
  };
}