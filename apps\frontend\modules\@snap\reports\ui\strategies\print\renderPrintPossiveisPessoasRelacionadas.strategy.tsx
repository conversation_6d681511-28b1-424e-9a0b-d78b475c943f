import React from 'react';
import { View, Text, StyleSheet, Svg, Rect, Image } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, getSingular, translateSource } from '../../../helpers';
import { getFieldLabel, getFieldValue, isValidUrl, isBase64Image } from '../helpers.strategy';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintPossiveisPessoasRelacionadasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      detalhes?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      telefones?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      imagens?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      enderecos?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      redes_sociais?: {
        [key: string]: {
          value: {
            [key: string]: ValueWithSource;
          };
          label: string;
          source: string[];
          is_deleted: boolean;
        };
      };
      [key: string]: any;
    }>;
  };
}

export const RenderPrintPossiveisPessoasRelacionadas: React.FC<RenderPrintPossiveisPessoasRelacionadasProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer} wrap={false}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((pessoa, pessoaIndex) => (
        <View key={`pessoa-${pessoaIndex}`} style={styles.pessoaContainer}>
          {/* Nome Completo */}
          {pessoa.nome_completo && !pessoa.nome_completo.is_deleted && (
            <View style={styles.nomeContainer}>
              <View style={styles.nomeLabelContainer}>
                <Text style={styles.nomeLabel}>
                  {(pessoa.nome_completo.label || "Nome Completo").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {pessoa.nome_completo.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.nomeValue}>{pessoa.nome_completo.value}</Text>
            </View>
          )}

          {/* Detalhes da Pessoa */}
          {pessoa.detalhes && pessoa.detalhes.length > 0 && (
            <View style={styles.detalhesContainer}>
              <View style={styles.detalhesGrid}>
                {pessoa.detalhes
                  .filter(detalhe => !detalhe.is_deleted)
                  .map((detalhe, detalheIndex) => (
                    Object.entries(detalhe.value)
                      .filter(([_, field]) => !field.is_deleted)
                      .map(([key, field], index) => (
                        <View key={`detalhe-${detalheIndex}-${index}`} style={styles.detailsCell}>
                          <View style={styles.infoContainer} wrap={false}>
                            <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                              <Rect width="8" height="8" fill='#CCCCCC' />
                            </Svg>
                            <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                            <Text style={styles.sourceText}>
                              {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                            </Text>
                          </View>
                          <Text style={styles.value}>{field.value}</Text>
                        </View>
                      ))
                  ))}
              </View>
            </View>
          )}

          {/* Telefones */}
          {pessoa.telefones && pessoa.telefones.length > 0 && (
            <View style={styles.telefonesContainer}>
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>TELEFONES</Text>
              </View>
              <View style={styles.phoneGrid}>
                {pessoa.telefones
                  .filter(telefone => !telefone.is_deleted)
                  .map((telefone, index) => (
                    <View key={`telefone-${index}`} style={styles.phoneBlock} wrap={false}>
                      <View style={styles.listContainer}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill="#889EA3" />
                        </Svg>
                        <Text style={styles.itemTitle}>
                          {translatePropToLabel(getSingular(telefone.label) || 'TELEFONE').toUpperCase()} {index + 1}
                        </Text>
                        <Text style={styles.sourceText}>
                          {telefone.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.fieldsGrid}>
                        {Object.entries(telefone.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Imagens */}
          {pessoa.imagens && pessoa.imagens.length > 0 && (
            <View style={styles.imagensContainer}>
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>IMAGENS</Text>
              </View>
              <View style={styles.imageGrid}>
                {pessoa.imagens
                  .filter(imagem => !imagem.is_deleted)
                  .map((imagem, index) => {
                    const urlField = Object.entries(imagem.value)
                      .find(([key, field]) => key === 'url' && !field.is_deleted);

                    if (!urlField) return null;
                    const [_, urlValue] = urlField;
                    const imageUrl = String(urlValue.value || "");
                    const isHttpUrl = isValidUrl(imageUrl) && !isBase64Image(imageUrl);

                    return (
                      <View key={`imagem-${index}`} style={styles.imageBlock} wrap={false}>
                        <View style={styles.listContainer}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(imagem.label) || 'IMAGEM').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {imagem.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>

                        {isHttpUrl && (
                          <Text style={styles.urlText}>{imageUrl}</Text>
                        )}

                        {(isValidUrl(imageUrl) || isBase64Image(imageUrl)) && (
                          <View style={styles.imageContainer} wrap={false}>
                            <Image src={imageUrl} style={styles.image} />
                          </View>
                        )}
                      </View>
                    );
                  })}
              </View>
            </View>
          )}

          {/* Endereços */}
          {pessoa.enderecos && pessoa.enderecos.length > 0 && (
            <View style={styles.enderecosContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>ENDEREÇOS</Text>
              </View>
              {pessoa.enderecos
                .filter(endereco => !endereco.is_deleted)
                .map((endereco, index) => (
                  <View key={`endereco-${index}`} style={styles.addressBlock} wrap={false}>
                    <View style={styles.listContainer}>
                      <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                        <Rect width="4" height="4" fill='#889EA3' />
                      </Svg>
                      <Text style={styles.itemTitle}>ENDEREÇO {index + 1}</Text>
                      <Text style={styles.sourceText}>
                        {endereco.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                      </Text>
                    </View>
                    <View style={styles.grid}>
                      {Object.entries(endereco.value)
                        .filter(([_, field]) => !field.is_deleted)
                        .map(([fieldKey, fieldValue], fieldIndex) => (
                          <View key={`field-${fieldIndex}`} style={styles.cell}>
                            <View style={styles.infoContainer}>
                              <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                <Rect width="8" height="8" fill='#CCCCCC' />
                              </Svg>
                              <Text style={styles.label}>
                                {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              </Text>
                              <Text style={styles.sourceText}>
                                {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                              </Text>
                            </View>
                            <Text style={styles.value}>
                              {String(fieldValue.value || "")}
                            </Text>
                          </View>
                        ))}
                    </View>
                  </View>
                ))}
            </View>
          )}

          {/* Redes Sociais */}
          {pessoa.redes_sociais && Object.keys(pessoa.redes_sociais).length > 0 && (
            <View style={styles.redesSociaisContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>REDES SOCIAIS</Text>
              </View>
              <View style={styles.socialGrid}>
                {Object.entries(pessoa.redes_sociais)
                  .filter(([_, redeSocial]) => !redeSocial.is_deleted)
                  .map(([redeSocialKey, redeSocial], redeSocialIndex) => (
                    <View key={`rede-${redeSocialIndex}`} style={styles.socialBlock} wrap={false}>
                      <View style={styles.listContainer}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill='#889EA3' />
                        </Svg>
                        <Text style={styles.itemTitle}>
                          {translatePropToLabel(redeSocial.label || redeSocialKey).toUpperCase()}
                        </Text>
                        <Text style={styles.sourceText}>
                          {redeSocial.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.fieldsGrid}>
                        {Object.entries(redeSocial.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>
                                  {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                </Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  nomeLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  pessoaContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  nomeContainer: {
    marginBottom: 12,
  },
  nomeLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  nomeValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  telefonesContainer: {
    marginBottom: 8,
  },
  imagensContainer: {
    marginBottom: 8,
  },
  enderecosContainer: {
    marginBottom: 8,
  },
  redesSociaisContainer: {
    marginBottom: 8,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  detalhesGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  grid: {
    paddingTop: 6,
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  // Telefone styles
  phoneGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  phoneBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  // Imagem styles
  imageGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  urlText: {
    paddingLeft: 8,
    fontSize: 8,
    marginBottom: 4,
    color: '#889EA3',
    maxWidth: '100%',
  },
  imageContainer: {
    paddingLeft: 8,
    marginTop: 4,
    alignItems: 'flex-start',
  },
  image: {
    width: 'auto',
    height: 'auto',
    maxWidth: '100%',
    maxHeight: 200,
    alignSelf: 'flex-start',
  },
  // Endereco styles
  addressBlock: {
    paddingLeft: 8,
    marginBottom: 12,
    borderBottom: '1pt solid #eee',
    paddingBottom: 8,
  },
  // Redes Sociais styles
  socialGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  socialBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
});