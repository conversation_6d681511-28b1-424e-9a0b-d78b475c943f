import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
/* render strategies */
import { RenderSociedades } from "./renderSociedades.strategy";
import { RenderVinculosEmpregaticios } from "./renderVinculosEmpregaticios.strategy";
import { RenderProcessos } from "./renderProcessos.strategy";
import { RenderRecursosPublicos } from "./renderRecursosPublicos.strategy";
import { RenderServicosPublicos } from "./renderServicosPublicos.strategy";
import { RenderFiliacaoPartidaria } from "./renderFiliacaoPartidaria.strategy";
import { RenderPossiveisContatos } from "./renderPossiveisContatos.strategy";
import { RenderDoacoesEnviadas } from "./renderDoacoesEnviadas.strategy";
import { RenderDoacoesRecebidas } from "./renderDoacoesRecebidas.strategy";
import { RenderFornecimentosEnviados } from "./renderFornecimentosEnviados.strategy";
import { RenderFornecimentosRecebidos } from "./renderFornecimentosRecebidos.strategy";
import { RenderJuntasComerciais } from "./renderJuntasComerciais.strategy";
import { RenderContatosSalvos } from "./renderContatosSalvos.strategy";
import { RenderPossiveisPessoasRelacionadas } from "./renderPossiveisPessoasRelacionadas.strategy";
import { RenderOutrasUrls } from "./renderOutrasUrls.strategy";
import { RenderPerfisRedesSociais } from "./renderRedesSociais.strategy";
import { RenderNomesUsuarios } from "./renderNomeUsuarios.strategy";

export const getStrategyMap = (): Record<string, any> => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];
  // CPF
  const strategySociedades = new RenderSociedades();
  const strategyVinculosEmpregaticios = new RenderVinculosEmpregaticios();
  const strategyProcessos = new RenderProcessos();
  const strategyRecursosPublicos = new RenderRecursosPublicos();
  const strategyServicosPublicos = new RenderServicosPublicos();
  const strategyFiliacaoPartidaria = new RenderFiliacaoPartidaria();
  const strategyPossiveisContatos = new RenderPossiveisContatos();
  const strategyDoacoesEnviadas = new RenderDoacoesEnviadas();
  const strategyDoacoesRecebidas = new RenderDoacoesRecebidas();
  const strategyFornecimentosEnviados = new RenderFornecimentosEnviados();
  const strategyFornecimentosRecebidos = new RenderFornecimentosRecebidos();
  // CNPJ
  const strategyJuntasComerciais = new RenderJuntasComerciais();
  // TELEFONE
  const strategyContatosSalvos = new RenderContatosSalvos();
  const strategyPossiveisPessoasRelacionadas = new RenderPossiveisPessoasRelacionadas();
  const strategyOutrasUrls = new RenderOutrasUrls();
  const strategyRedesSociais = new RenderPerfisRedesSociais();
  const strategyNomesUsuarios = new RenderNomesUsuarios();

  // CPF
  const cpfStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.sociedades]: strategySociedades,
    [REPORT_SECTIONS.vinculos_empregaticios]: strategyVinculosEmpregaticios,
    [REPORT_SECTIONS.processos]: strategyProcessos,
    [REPORT_SECTIONS.recursos_publicos_recebidos]: strategyRecursosPublicos,
    [REPORT_SECTIONS.servico_publico]: strategyServicosPublicos,
    [REPORT_SECTIONS.filiacao_partidaria]: strategyFiliacaoPartidaria,
    [REPORT_SECTIONS.possiveis_contatos]: strategyPossiveisContatos,
    [REPORT_SECTIONS.doacoes_enviadas_campanha]: strategyDoacoesEnviadas,
    [REPORT_SECTIONS.doacoes_recebidas_campanha]: strategyDoacoesRecebidas,
    [REPORT_SECTIONS.fornecimentos_enviados_campanha]: strategyFornecimentosEnviados,
    [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: strategyFornecimentosRecebidos,
  };

  // CNPJ
  const cnpjStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.juntas_comerciais]: strategyJuntasComerciais,
    [REPORT_SECTIONS.processos]: strategyProcessos,
    [REPORT_SECTIONS.recursos_publicos_recebidos]: strategyRecursosPublicos,
    [REPORT_SECTIONS.doacoes_enviadas_campanha]: strategyDoacoesEnviadas,
    [REPORT_SECTIONS.doacoes_recebidas_campanha]: strategyDoacoesRecebidas,
    [REPORT_SECTIONS.fornecimentos_enviados_campanha]: strategyFornecimentosEnviados,
    [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: strategyFornecimentosRecebidos
  };

  // TELEFONE
  const telefoneStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.contatos_salvos]: strategyContatosSalvos,
    [REPORT_SECTIONS.empresas_relacionadas]: strategySociedades,
    [REPORT_SECTIONS.nomes_usuario]: strategyNomesUsuarios,
    [REPORT_SECTIONS.perfis_redes_sociais]: strategyRedesSociais,
    [REPORT_SECTIONS.outras_urls]: strategyOutrasUrls,
    [REPORT_SECTIONS.possiveis_contatos]: strategyPossiveisContatos,
    [REPORT_SECTIONS.possiveis_pessoas_relacionadas]: strategyPossiveisPessoasRelacionadas,
  }

  switch (reportType) {
    case "cnpj":
      return cnpjStrategyMap;
    case "telefone":
      return telefoneStrategyMap;
    case "cpf":
    default:
      return cpfStrategyMap;
  }
};