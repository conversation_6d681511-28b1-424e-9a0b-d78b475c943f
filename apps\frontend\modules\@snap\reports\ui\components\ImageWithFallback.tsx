import { useState } from 'react'
import { MdImageNotSupported } from 'react-icons/md'

interface ImageWithFallbackProps {
  src?: string | null
  alt?: string
  className?: string
}

/**
 * Renders an <img> in a fixed (but responsive) box,
 * object-fit: contain so it never distorts,
 * and swaps to a placeholder icon on error.
 */
export function ImageWithFallback({
  src,
  alt = 'Imagem',
  className = '',
}: ImageWithFallbackProps) {
  // const [failed, setFailed] = useState(false)
  if (!src) return null

  // if (!src || failed) {
  //   return (
  //     <div
  //       className={`
  //         ${className}
  //         flex items-center justify-center
  //         bg-gray-100 text-gray-400
  //       `}
  //     >
  //       <MdImageNotSupported className="w-12 h-12" />
  //     </div>
  //   )
  // }

  return (
    <div
      className={`
        ${className}
        overflow-hidden
      `}
    >
      <img
        src={src}
        alt={alt}
        // onError={() => setFailed(true)}
        className="
          w-full h-full
          object-contain
          block
        "
      />
    </div>
  )
}
