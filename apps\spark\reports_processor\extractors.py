import json
from copy import copy
from typing import Dict, <PERSON><PERSON>, List

from pyspark.sql import DataFrame, functions as F
from pyspark.sql.types import ArrayType, StructType
from pyspark.sql.functions import explode, col

from reports_processor.CallbackProcessors import CallbackProcessors
from reports_processor.EntityExtractor import ExtractionR<PERSON>ult, EntityExtractor, VinculoConfig
from reports_processor.FormatFrontFormat import front_vinculo_pessoais
from reports_processor.constants import *
from reports_processor.formatters import (
    merge_entity_lists, final_front_format
)
from reports_processor.reports.ReportProcessorFactory import ReportProcessorFactory
from reports_processor.utils import (
    get_dtype, is_a_match, get_constant, basic_property_dict
)


def extract_entities_from_dataframe(df: DataFrame, report_type: ReportType, search_value: str) -> List[Dict]:
    """
    Main function to extract entities from DataFrame with improved performance and maintainability
    """
    extractor = EntityExtractor()

    # Extract basic entity data
    main_entity_data, other_result_data, main_entity_sources = _extract_basic_entity_data(
        df, report_type, search_value
    )

    # Configure extraction operations
    extraction_configs = _get_extraction_configs(extractor, report_type, search_value, other_result_data, bool(main_entity_data))

    # Execute all extractions
    extraction_results = {}
    for key, config in extraction_configs.items():
        try:
            if hasattr(config, 'extract_func'):
                result = config.extract_func(other_result_data, report_type.main_entity, search_value, config)
                extraction_results[key] = final_front_format(config.front_format, result.sources, result.data)
            else:
                raise NotImplementedError(f'extract_func not implemented for {key}')
        except Exception as e:
            logger.error(f"Error extracting {key}: {e}")
            extraction_results[key] = final_front_format(config.front_format, [], [])

    # Format main entity data
    main_form = final_front_format(front_vinculo_pessoais, main_entity_sources, [main_entity_data])

    # Combine all results
    final_data = [main_form] + [result for result in extraction_results.values() if result]

    return final_data


def _extract_basic_entity_data(
        df: DataFrame,
        report_type,
        search_value: str
) -> Tuple[Dict, Dict, set]:
    """Extract basic entity data from DataFrame"""
    main_entity_data = {}
    main_entity_sources = set()
    other_result_data = {}

    def explode_and_select(df: DataFrame, column: str) -> DataFrame:
        try:
            exploded_df = df.withColumn(column, explode(col(column)))

            # Get the data type of the exploded column
            field_type = [f for f in exploded_df.schema.fields if f.name == column][0].dataType

            if isinstance(field_type, StructType):
                # If it's a struct, we can use `column.*`
                return exploded_df.select(f"{column}.*")
            else:
                # Otherwise just return the exploded column
                return exploded_df.select(column)
        except Exception as e:
            logging.error(f"failing to expand df ===> Before explode_and_select {e}")
            df.printSchema()
            df.select(column).show(truncate=False)
            raise

    for base_column in df.columns:
        if base_column == ReportKeys.METADATA:
            continue

        df_entity = explode_and_select(df, base_column)

        if API_ERROR_MESSAGE_KEY in df_entity.columns:
            has_no_results = df_entity.filter(F.col(API_ERROR_MESSAGE_KEY) == NO_RESULTS_API_MESSAGE).limit(1).count() > 0
            if has_no_results:
                logger.info(f"{base_column} has no results.")
            else:
                logger.warning(f"Skipping data from {base_column} due to error.")
            continue

        _process_dataframe_column(
            df_entity, base_column, report_type, search_value,
            main_entity_data, main_entity_sources, other_result_data
        )

    return main_entity_data, other_result_data, main_entity_sources


def _process_dataframe_column(
        df_entity: DataFrame,
        base_column: str,
        report_type,
        search_value: str,
        main_entity_data: Dict,
        main_entity_sources: set,
        other_result_data: Dict
):
    """Process a single DataFrame column"""
    for sub_column in df_entity.columns:
        if not isinstance(get_dtype(df_entity, sub_column), ArrayType):
            continue

        df_sub_exploded = df_entity.withColumn(sub_column, F.explode(sub_column))

        if isinstance(get_dtype(df_sub_exploded, sub_column), StructType):
            df_result = df_sub_exploded.select(f"{sub_column}.*")
        else:
            df_result = df_sub_exploded.select(sub_column)

        entities = df_result.toJSON().map(lambda j: json.loads(j)).collect()

        if (report_type is ReportType.PHONE or report_type is ReportType.EMAIL) and sub_column in report_type.main_entity:
            if len(entities) > 1:
                for entity in entities:
                    _add_other_entity(base_column, sub_column, entity, other_result_data)
            else:
                _process_main_entity(entities[0], base_column, main_entity_data, other_result_data)
                main_entity_sources.add(base_column)

        else:
            for entity in entities:

                if sub_column == report_type.main_entity and is_a_match(entity, sub_column, search_value):
                    _process_main_entity(entity, base_column, main_entity_data, other_result_data)
                    main_entity_sources.add(base_column)
                else:
                    _add_other_entity(base_column, sub_column, entity, other_result_data)



def _process_main_entity(
        entity: Dict,
        base_column: str,
        main_entity_data: Dict,
        other_result_data: Dict
):
    """Process main entity data"""
    for k, v in entity.items():
        if k in skip_fields:
            continue
        elif isinstance(v, str):
            existing = main_entity_data.get(k)
            if existing:
                if existing["value"] == v:
                    existing["source"].add(base_column)
                else:
                    variant_key = f"{k}_{base_column}"
                    main_entity_data[variant_key] = basic_property_dict(
                        v, get_constant(Constants.PropToLabel, k, k), {base_column}
                    )
            else:
                main_entity_data[k] = basic_property_dict(
                    v, get_constant(Constants.PropToLabel, k, k), {base_column}
                )
        else:
            new_base = other_result_data.setdefault(base_column, {})
            if k in new_base:
                if isinstance(v, list) and isinstance(new_base[k], list):
                    new_base[k].extend(v)
                else:
                    logger.warning(f"Losing unmapped property {k}, value: {v}")
            else:
                new_base[k] = v


def _add_other_entity(
        base_column: str,
        sub_column: str,
        entity: Dict,
        other_result_data: Dict
):
    """Add other entity to result data"""
    new_base = other_result_data.setdefault(base_column, {})
    if sub_column in new_base:
        new_base[sub_column] = merge_entity_lists(new_base[sub_column], [entity])
    else:
        new_base[sub_column] = [entity]

def _get_extraction_configs(
        extractor: EntityExtractor,
        report_type: ReportType,
        search_value: str,
        other_result_data: Dict,
        has_main_data: bool = True,
) -> Dict[str, VinculoConfig]:
    """Get extraction configurations for all relationship types"""

    # Create the appropriate processor
    processor = ReportProcessorFactory.create_processor(extractor, report_type)
    entity_type = report_type.main_entity
    return processor.get_section_vinculo_config(other_result_data, entity_type, search_value, has_main_data)