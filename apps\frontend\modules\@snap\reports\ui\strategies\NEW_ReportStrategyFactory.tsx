import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
// Hooks para renderizar as seções
import { useRenderDadosPessoais } from "./NEW_renderDadosPessoais.strategy";
import { useRenderEmails } from "./NEW_renderEmails.strategy";
import { useRenderEnderecos } from "./NEW_renderEnderecos.strategy";
import { useRenderDiariosOficiais } from "./NEW_renderDiariosOficiais.strategy";
import { useRenderTelefonesArray } from "./NEW_renderTelefones.strategy";
import { useRenderPossiveisContas } from "./NEW_renderPossiveisContas.strategy";
import { useRenderSocios } from "./NEW_renderSocios.strategy"
import { useRenderParentes } from "./NEW_renderParentes.strategy";
import { useRenderMandados } from "./NEW_renderMandados.strategy";
import { useRenderRedesSociais } from "./NEW_renderRedesSociais.strategy";
import { useRenderImagens } from "./NEW_renderImagens.strategy";

export const useNewStrategyMap = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // COMUM PARA TODOS OS TIPOS
  const common_base_map: Record<string, any> = {
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
  };

  // CPF, TELEFONE e EMAIL
  const cpf_telefone_email_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoais(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.parentes]: useRenderParentes(REPORT_SECTIONS.parentes),
    [REPORT_SECTIONS.imagens]: useRenderImagens(REPORT_SECTIONS.imagens),
    // [REPORT_SECTIONS.possiveis_contatos]: (section) => (
    //   <RenderPrintPossiveisContatos section={section} />
    // ),
    [REPORT_SECTIONS.possiveis_contas_em_sites]: useRenderPossiveisContas(REPORT_SECTIONS.possiveis_contas_em_sites),
    [REPORT_SECTIONS.perfis_redes_sociais]: useRenderRedesSociais(REPORT_SECTIONS.perfis_redes_sociais),
  };

  // CPF e CNPJ
  const cpf_cnpj_map: Record<string, any> = {
    // [REPORT_SECTIONS.processos]: (section) => (
    //   <RenderPrintProcessos section={section} />
    // ),
    [REPORT_SECTIONS.socios]: useRenderSocios(REPORT_SECTIONS.socios),
    // [REPORT_SECTIONS.recursos_publicos_recebidos]: (section) => (
    //   <RenderPrintRecursosPublicos section={section} />
    // ),
    // [REPORT_SECTIONS.doacoes_enviadas_campanha]: (section) => (
    //   <RenderPrintDoacoesEnviadas section={section} />
    // ),
    // [REPORT_SECTIONS.doacoes_recebidas_campanha]: (section) => (
    //   <RenderPrintDoacoesRecebidas section={section} />
    // ),
    // [REPORT_SECTIONS.fornecimentos_enviados_campanha]: (section) => (
    //   <RenderPrintFornecimentosEnviados section={section} />
    // ),
    // [REPORT_SECTIONS.fornecimentos_recebidos_campanha]: (section) => (
    //   <RenderPrintFornecimentosRecebidos section={section} />
    // ),
    [REPORT_SECTIONS.diarios_oficiais_nome]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_nome),
  };

  // TELEFONE e EMAIL
  const telefone_email_map: Record<string, any> = {
    // [REPORT_SECTIONS.empresas_relacionadas]: (section) => (
    //   <RenderPrintSociedades section={section} />
    // ),
    // [REPORT_SECTIONS.nomes_usuario]: (section) => (
    //   <RenderPrintNomeUsuarios section={section} />
    // ),
    // [REPORT_SECTIONS.perfis_redes_sociais]: (section) => (
    //   <RenderPrintRedesSociais section={section} />
    // ),
    // [REPORT_SECTIONS.outras_urls]: (section) => (
    //   <RenderPrintOutrasUrls section={section} />
    // ),
    // [REPORT_SECTIONS.possiveis_pessoas_relacionadas]: (section) => (
    //   <RenderPrintPossiveisPessoasRelacionadas section={section} />
    // ),
  };

  // CPF
  const cpf_map: Record<string, any> = {
    [REPORT_SECTIONS.mandados_de_prisao]: useRenderMandados(REPORT_SECTIONS.mandados_de_prisao),
    // [REPORT_SECTIONS.sociedades]: (section) => (
    //   <RenderPrintSociedades section={section} />
    // ),
    // [REPORT_SECTIONS.vinculos_empregaticios]: (section) => (
    //   <RenderPrintVinculosEmpregaticios section={section} />
    // ),
    [REPORT_SECTIONS.diarios_oficiais_cpf]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cpf),
    // [REPORT_SECTIONS.servico_publico]: (section) => (
    //   <RenderPrintServicosPublicos section={section} />
    // ),
    // [REPORT_SECTIONS.filiacao_partidaria]: (section) => (
    //   <RenderPrintFiliacaoPartidaria section={section} />
    // ),
  };

  // CNPJ
  const cnpj_map: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoais(REPORT_SECTIONS.dados_pessoais), // TODO - MUDAR para Dados Empresa
    // [REPORT_SECTIONS.juntas_comerciais]: (section) => (
    //   <RenderPrintJuntasComerciais section={section} />
    // ),
    [REPORT_SECTIONS.diarios_oficiais_cnpj]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cnpj),
  };

  switch (reportType) {
    case "cnpj":
      return {
        ...common_base_map,
        ...cpf_cnpj_map,
        ...cnpj_map,
      };
    case "telefone":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
      };
    case "email":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...telefone_email_map,
      };
    case "cpf":
      return {
        ...common_base_map,
        ...cpf_telefone_email_map,
        ...cpf_cnpj_map,
        ...cpf_map
      };
    default:
      return common_base_map;
  }
};