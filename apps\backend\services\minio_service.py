import json
import logging
from io import BytesIO

from core.config import minio_client

logger = logging.getLogger(__name__)


async def save_to_minio(bucket_name: str, object_name: str, data: dict, user_id: str):
    logger.info("[save_to_minio][user(%s)] Saving object %s to bucket %s", user_id, object_name, bucket_name)
    try:
        if not minio_client.bucket_exists(bucket_name):
            logger.warning("[save_to_minio][user(%s)] Bucket %s not found. Creating...", user_id, bucket_name)
            minio_client.make_bucket(bucket_name)

        json_bytes = json.dumps(data, indent=2).encode("utf-8")
        json_stream = BytesIO(json_bytes)

        minio_client.put_object(
            bucket_name,
            object_name,
            data=json_stream,
            length=len(json_bytes),
            content_type="application/json"
        )
        logger.info("[save_to_minio][user(%s)] Upload completed", user_id)

    except Exception as e:
        logger.error("[save_to_minio] Failed to save object: %s", e)


async def load_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[load_from_minio][user(%s)] Loading object %s", user_id, object_name)
    try:
        response = minio_client.get_object(bucket_name, object_name)
        data = json.loads(response.read())
        data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
        logger.info(f"[load_from_minio][user({user_id})] Retrieved object with keys: {data_keys}")

        return data
    except Exception as e:
        logger.error("[load_from_minio][user(%s)] Failed to load object %s: %s", user_id, object_name, e)
        return None


async def delete_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[delete_from_minio][user(%s)] Deleting object %s", user_id, object_name)
    try:

        minio_client.remove_object(bucket_name, object_name)
        minio_client.remove_object("reports", object_name)
        logger.info("[delete_from_minio][user(%s)] Object %s deleted after load", user_id, object_name)

    except Exception as e:
        logger.error("[delete_from_minio][user(%s)] Failed to delete object %s: %s", user_id, object_name, e)
