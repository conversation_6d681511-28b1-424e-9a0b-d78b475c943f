import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintRecursosPublicosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      orgao?: ValueWithSource;
      detalhes?: {
        [key: string]: ValueWithSource
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintRecursosPublicos: React.FC<RenderPrintRecursosPublicosProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((recurso, recursoIndex) => (
        <View key={`recurso-${recursoIndex}`} style={styles.recursoContainer}>
          {/* Nome do Órgão */}
          {recurso.orgao && !recurso.orgao.is_deleted && (
            <View style={styles.orgaoContainer}>
              <View style={styles.orgaoLabelContainer}>
                <Text style={styles.orgaoLabel}>
                  {(recurso.orgao.label || "Nome do Órgão").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {recurso.orgao.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.orgaoValue}>{recurso.orgao.value}</Text>
            </View>
          )}

          {/* Detalhes do Recurso */}
          {recurso.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(recurso.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  orgaoLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  recursoContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  orgaoContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  orgaoLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  orgaoValue: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});