import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintDoacoesEnviadasProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      candidato?: ValueWithSource;
      vinculos?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      detalhes?: {
        [key: string]: ValueWithSource
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintDoacoesEnviadas: React.FC<RenderPrintDoacoesEnviadasProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}  >
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill="#FE473C" />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((doacao, index) => (
        <View key={`doacao-${index}`} style={styles.doacaoContainer}>
          {/* Candidato */}
          {doacao.candidato && !doacao.candidato.is_deleted && (
            <View style={styles.candidatoContainer}  >
              <View style={styles.candidatoLabelContainer}>
                <Text style={styles.candidatoLabel}>
                  {(doacao.candidato.label || "Candidato").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {doacao.candidato.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.candidatoValue}>{doacao.candidato.value}</Text>
            </View>
          )}

          {/* Detalhes */}
          {doacao.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(doacao.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], detailIndex) => (
                    <View key={`detalhe-${detailIndex}`} style={styles.detailsCell}  >
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{String(field.value)}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Vínculos */}
          {doacao.vinculos && doacao.vinculos.length > 0 && (
            <View style={styles.vinculosContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>DOAÇÕES</Text>
              </View>
              {doacao.vinculos
                .filter(vinculo => !vinculo.is_deleted)
                .map((vinculo, vinculoIndex) => (
                  <View key={`vinculo-${vinculoIndex}`} style={styles.vinculoBlock} wrap={false}>
                    <View style={styles.listContainer}>
                      <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                        <Rect width="4" height="4" fill='#889EA3' />
                      </Svg>
                      <Text style={styles.itemTitle}>
                        {'DOAÇÃO'} {vinculoIndex + 1}
                      </Text>
                      <Text style={styles.sourceText}>
                        {vinculo.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                      </Text>
                    </View>
                    <View style={styles.grid}>
                      {Object.entries(vinculo.value)
                        .filter(([_, field]) => !field.is_deleted)
                        .map(([fieldKey, fieldValue], fieldIndex) => (
                          <View key={`field-${fieldIndex}`} style={styles.cell}>
                            <View style={styles.infoContainer}>
                              <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                <Rect width="8" height="8" fill='#CCCCCC' />
                              </Svg>
                              <Text style={styles.label}>
                                {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              </Text>
                              <Text style={styles.sourceText}>
                                {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                              </Text>
                            </View>
                            <Text style={styles.value}>
                              {String(fieldValue.value || "")}
                            </Text>
                          </View>
                        ))}
                    </View>
                  </View>
                ))}
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  candidatoLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 4,
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  doacaoContainer: {
    marginBottom: 8,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  candidatoContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  candidatoLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  candidatoValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  vinculosContainer: {
    marginBottom: 8,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  vinculoBlock: {
    marginBottom: 10,
    paddingLeft: 8,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
});