import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintServicosPublicosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      nome_completo?: ValueWithSource;
      remuneracao?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
      detalhes?: {
        [key: string]: ValueWithSource
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintServicosPublicos: React.FC<RenderPrintServicosPublicosProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill="#FE473C"/>
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      
      {section.data.map((servicoPublico, index) => (
        <View key={`servico-${index}`} style={styles.servicoContainer}>
          {/* Nome Completo */}
          {servicoPublico.nome_completo && !servicoPublico.nome_completo.is_deleted && (
            <View style={styles.nomeContainer}>
              <View style={styles.nomeLabelContainer}>
                <Text style={styles.nomeLabel}>
                  {(servicoPublico.nome_completo.label || "Nome Completo").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {servicoPublico.nome_completo.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.nomeValue}>{servicoPublico.nome_completo.value}</Text>
            </View>
          )}

          {/* Detalhes */}
          {servicoPublico.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(servicoPublico.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], detailIndex) => (
                    <View key={`detalhe-${detailIndex}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Remuneração */}
          {servicoPublico.remuneracao && servicoPublico.remuneracao.length > 0 && (
            <View style={styles.remuneracaoContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>REMUNERAÇÃO</Text>
              </View>
              <View style={styles.remuneracaoGrid}>
                {servicoPublico.remuneracao
                  .filter(remuneracao => !remuneracao.is_deleted)
                  .map((remuneracao, remIndex) => (
                    <View key={`remuneracao-${remIndex}`} style={styles.remuneracaoBlock}>
                      <View style={styles.listContainer}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill="#889EA3" />
                        </Svg>
                        <Text style={styles.itemTitle}>
                          {translatePropToLabel(remuneracao.label || 'REMUNERAÇÃO').toUpperCase()} {remIndex + 1}
                        </Text>
                        <Text style={styles.sourceText}>
                          {remuneracao.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.fieldsGrid}>
                        {Object.entries(remuneracao.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  nomeLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  servicoContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  nomeContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  nomeLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  nomeValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  remuneracaoContainer: {
    marginBottom: 8,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  remuneracaoGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  remuneracaoBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
});