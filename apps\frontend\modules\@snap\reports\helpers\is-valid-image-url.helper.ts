export const isValidImageUrl = (url?: string | null): boolean => {
  if (!url) return false;

  try {
    const parsed = new URL(url);
    const pathname = parsed.pathname.toLowerCase();
    const validExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', 
      '.bmp', '.tiff', '.ico', '.apng'
    ];
    
    return validExtensions.some(ext => pathname.endsWith(ext));
  } catch {
    return false;
  }
};