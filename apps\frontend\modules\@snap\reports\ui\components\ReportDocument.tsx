import {
  Document,
  Page,
  View,
  Text,
  StyleSheet,
  Image,
  Svg,
  Rect,
} from '@react-pdf/renderer';
import { ReportMetadata, ReportSection } from '../../global';
import { usePrintStrategyMap } from '../strategies/print/printStrategyFactory';
import PrintProfileHeader from './PrintProfileHeader';
import PrintSummary from './PrintSummary';
import { REPORT_CONSTANTS } from '../../config/constants';

type ReportSearchArgs = { [key: string]: string };
type ReportType = 'cpf' | 'telefone' | 'email' | 'cnpj';

export interface ReportDocumentProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
}

export default function ReportDocument({
  sections,
  metadata,
}: ReportDocumentProps) {
  const title =
    (metadata[REPORT_CONSTANTS.new_report.report_name] as string) ||
    "Relatório";
  const reportType = (metadata[REPORT_CONSTANTS.new_report.report_type] as ReportType) || '';
  const reportSearchArgs = (metadata[REPORT_CONSTANTS.new_report.report_search_args] as ReportSearchArgs) || {};

  const printMap = usePrintStrategyMap(reportType);
  const printableSections = Array.isArray(sections)
    ? sections.filter(
      (section) =>
        typeof section.title === 'string' &&
        !section.subsection &&
        printMap[section.title]
    )
    : [];

  const searchValue = Object.values(reportSearchArgs)[0] || '';
  const iconSrc = `/assets/print-${reportType}.png`;

  const summaryItems = printableSections.map((section) => ({
    title: section.title,
    data_count: section.data_count,
  }));

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header} fixed>
          <View style={styles.logoContainer}>
            <Image src="/pwa-192x192.png" style={styles.logo} />
          </View>

          <View style={styles.headerContent}>
            <Text style={styles.title}>{title.toUpperCase()}</Text>
            <View style={styles.reportTypeContainer}>
              {['cpf', 'telefone', 'email', 'cnpj'].includes(reportType) ? (
                <Image src={iconSrc} style={styles.searchIcon} />
              ) : (
                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                  <Rect width="8" height="8" fill="#FE473C" />
                </Svg>
              )}
              <Text style={styles.searchValue}>{searchValue}</Text>
            </View>
          </View>

          <Image src="/assets/grafismo.png" style={styles.spiralImage} />
        </View>


        <View style={styles.customHeader}>
          <PrintProfileHeader metadata={metadata} report_type={reportType} />
          <PrintSummary items={summaryItems} searchType={reportType as string} searchValue={searchValue as string} />
        </View>

        {/* Seções */}
        {printableSections.map((section, idx) => {
          const Renderer = printMap[section.title];
          return (
            Renderer ? Renderer(section) : null
          );
        })}

        {/* Footer */}
        <View style={styles.footer} fixed>
          <Image
            src="/assets/grafismo_branco.png"
            style={styles.footerSpiralImage}
          />
          <View style={styles.footerContent}>
            <Text
              style={styles.pageNumber}
              render={({ pageNumber, totalPages }) =>
                `Página ${pageNumber
                  ?.toString()
                  ?.padStart(2, '0')} de ${totalPages
                    ?.toString()
                    ?.padStart(2, '0')}`
              }
            />
          </View>
        </View>
      </Page>
    </Document>
  );
}

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 12,
    paddingTop: 80,
    paddingBottom: 60,
    paddingHorizontal: 20,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 70,
    backgroundColor: '#E5E5EA',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    overflow: 'hidden',
  },
  logoContainer: { paddingTop: 2, marginRight: 20 },
  logo: { width: 24, height: 'auto' },
  headerContent: { flex: 1, flexDirection: 'column', justifyContent: 'center' },
  title: { fontSize: 16 },
  reportTypeContainer: { flexDirection: 'row', alignItems: 'flex-start' },
  searchIcon: { width: 10, height: 10, marginRight: 4 },
  searchValue: { fontSize: 10 },
  spiralImage: {
    position: 'absolute',
    right: -110,
    top: -60,
    width: 300,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  footerSpiralImage: {
    position: 'absolute',
    left: -120,
    bottom: -148,
    width: 350,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  sectionTitle: { fontSize: 18, marginBottom: 8 },
  sectionBody: { marginBottom: 12 },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: "#889EA3",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    overflow: "hidden",
  },
  footerContent: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  pageNumber: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'normal',
  },
  customHeader: {
    paddingTop: 120,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
});