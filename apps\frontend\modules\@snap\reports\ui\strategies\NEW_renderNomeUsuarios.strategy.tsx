import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { NomeUsuario } from "../../model/NomeUsuario";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderNomeUsuarios(sectionTitle: string): ArrayRenderStrategy<NomeUsuario> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean => {
    return entry.detalhes
      ? entry.detalhes.every((detalhe: any) =>
        detalhe.value?.alias?.is_deleted === true || detalhe.is_deleted === true
      )
      : false;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // contar itens não deletados (cada objeto de nome de usuário)
      const nonDeletedDetalhes = entry.detalhes.filter(
        (detalhe: any) =>
          detalhe.value?.alias?.is_deleted !== true && detalhe.is_deleted !== true
      ).length;

      return count + nonDeletedDetalhes;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (nomesUsuarios?: NomeUsuario) => React.ReactElement | null
  > = {
    detalhes: (nomesUsuarios) => {
      if (!nomesUsuarios?.detalhes?.length) return null;

      const filteredDetalhes = nomesUsuarios.detalhes.filter((detalhe) => {
        const aliasDeleted = detalhe.value?.alias?.is_deleted === true;
        const detalheDeleted = detalhe.is_deleted === true;
        return shouldInclude(aliasDeleted || detalheDeleted);
      });

      if (filteredDetalhes.length === 0) return null;

      const onToggleDetalhe = (detalheIndex: number) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          entry => {
            const detalhe = (entry as any).detalhes?.[detalheIndex];
            if (detalhe?.value?.alias) {
              detalhe.value.alias.is_deleted = !detalhe.value.alias.is_deleted;
            }
          },
          testEntryDeleted,
          testSectionDeleted,
          calculateDataCount
        );
      };

      return (
        <CustomGridContainer cols={2}>
          {filteredDetalhes.map((detalhe) => {
            const originalIndex = nomesUsuarios.detalhes.indexOf(detalhe);
            const aliasValue = detalhe.value?.alias?.value;

            return (
              <CustomGridItem
                key={`nome-usuario-detalhe-column-${originalIndex}`}
                cols={1}
                onToggleField={() => onToggleDetalhe(originalIndex)}
              >
                <div className="py-2 group">
                  <CustomReadOnlyInputField
                    label={`${(detalhe.label || 'ALIAS').toUpperCase()} ${originalIndex + 1}`}
                    value={String(aliasValue || '')}
                    tooltip={renderSourceTooltip(detalhe.value?.alias?.source)}
                  />
                </div>
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof NomeUsuario>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (nomesUsuarios: NomeUsuario): React.ReactElement[] => {
    const keys = Object.keys(nomesUsuarios) as Array<keyof NomeUsuario>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Nomes de Usuário] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(nomesUsuarios))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: NomeUsuario[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Nomes de Usuário] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((detalhe: any) =>
          detalhe.value?.alias?.is_deleted === true || detalhe.is_deleted === true
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((nomesUsuarios, index) => {
      const elements = renderSingleItem(nomesUsuarios);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`nomes-usuarios-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca o alias como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value?.alias) {
              detalhe.value.alias.is_deleted = targetDeletedState;
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}