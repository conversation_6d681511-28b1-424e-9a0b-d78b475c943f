import logging
import uuid
from datetime import datetime, timezone

import httpx
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Depends, FastAPI
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.inspection import inspect

from core.config import settings
from core.constants import (ReportInputs, Fields, ReportOutputs, ReportMessages, Endpoints, CustomErrorMessages,
                            MergedReport)
from database.db import get_db
from models.report_model import UserReports
from schemas.report_schema import UpdateReportRequest, SnapApiRequest, InsertReport, InsertVerifier
from services.SnapStatusTracker import SnapStatusTracker
from services.auth_service import auth_guard
from utils.db_utils import retry_db_operation
from utils.report_utils import (get_field_from_request, update_blank_report,
                                get_report, list_user_reports,
                                update_existing_report, save_merged_report, insert_verifier_on_db, insert_hmacs)
from utils.user_utils import get_user_data_dict

logger = logging.getLogger(__name__)

async def insert_report_handler(body: InsertReport, user: dict, user_reports_id: str, db: AsyncSession):
    logger.info("[insert_report_handler][user(%s)] Inserting new report", user)
    user_id = user.get("sub")

    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        logger.error("[insert_report_handler] Invalid user_id UUID format")
        # return None

    await update_blank_report(
        db=db,
        user_uuid=user_uuid,
        user_reports_id=user_reports_id,
        report_name=body.report_name,
        report_type=body.report_type,
        report_status=body.report_status,
        report_search_args=body.report_search_args,
        subject_name=body.subject_name,
        subject_mother_name=body.subject_mother_name,
        subject_age=body.subject_age,
        subject_sex=body.subject_sex,
        creation_at=body.creation_at,
        modified_at=body.modified_at,
        omitted_notes=body.omitted_notes,
        data=body.data
    )
    if body.hmac: #TODO REMOVE THIS CONDITIONAL WHEN WE START TO USE
        await insert_hmacs(db=db, user_uuid= user_uuid, user_reports_id=user_reports_id, hmac=body.hmac)




async def validate_access_to_report(db, user_id, report_type):
    logger.info("[validate_access_to_report] Validating access for user")
    user_report_types = ['cpf', 'cnpj']
    user_credits = 1000

    if report_type not in user_report_types:
        logger.warning("[validate_access_to_report] User does not have access to report type")
        raise HTTPException(**CustomErrorMessages.no_access.to_dict(report_type))

    if user_credits <= 0:
        logger.warning("[validate_access_to_report] User has no credits")
        raise HTTPException(**CustomErrorMessages.no_credits.to_dict(report_type))
    



async def get_saved_reports_handler(db: AsyncSession, user: dict, limit: int,  page: int,
                                    order: str, column_order: str, hmac_filter: str, hmac_column: str):
    logger.info("[get_saved_reports_handler] Fetching saved reports")

    try:
        user_id = user.get("sub")

        return await retry_db_operation(
            lambda: list_user_reports(
                db=db,
                user_id=user_id,
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column

            )
        )

    except Exception as e:
        logger.error("[get_saved_reports_handler] Failed: %s", e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_users_reports.to_dict())



async def get_one_report_handler(report_id: str, user: dict, db: AsyncSession):
    logger.info("[get_one_report_handler][user(%s)] Fetching one report", user)
    user_id = user.get("sub")
    return await get_report(db, user_id, report_id)



async def update_report_handler(body: UpdateReportRequest, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[update_report_handler] Updating a report")

    report_id = str(body.report_id)
    user_id = str(body.user_id)
    new_name = body.report_name
    omitted_notes = body.omitted_notes

    if new_name is None:
        report_data = await get_report(db, user_id, report_id)
        if report_data:
            return {column.key: getattr(report_data, column.key) for column in inspect(UserReports).mapper.column_attrs}
        else:
            return None

    await update_existing_report(db=db, report_id=report_id, omitted_notes=omitted_notes, report_name=new_name)

    return {
        ReportOutputs.message: ReportMessages.updated,
        Fields.report_name: new_name,
        ReportOutputs.report_id: report_id
    }


async def merge_reports_handler(request: Request, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[merge_reports_handler] Merging reports")

    data = await request.json()
    user_id = get_field_from_request(data, Fields.user_id)
    report_id_list = get_field_from_request(data, ReportInputs.report_id)

    if len(report_id_list) < 2:
        logger.warning("[merge_reports_handler] Invalid number of reports for merge")
        raise HTTPException(status_code=400, detail="Invalid number of reports")

    user_dict = await get_user_data_dict(db, user_id)
    report_type = MergedReport.REPORT_TYPE
    report_name = get_field_from_request(data, Fields.report_name, raise_error=False)
    _, report_cost = await validate_access_to_report(db, user_dict, user_id, report_type)

    report_data_list = []
    for report_id in report_id_list:
        report_snapshot = await get_report(db, user_id, report_id)
        if not report_snapshot:
            raise HTTPException(status_code=400, detail=f"Invalid report ID {report_id}")
        report_data_list.append({key: getattr(report_snapshot, key) for key in UserReports.__table__.columns.keys()})

    merged_report = MergedReport(*report_data_list)
    base_report_dict = merged_report.make_merged_report()

    last_modified_date = datetime.now(timezone.utc)

    return await save_merged_report(db, user_id, report_cost,   last_modified_date, base_report_dict, report_type, report_name)


async def get_data_from_snap_api_handler(body: SnapApiRequest, user: dict, db: AsyncSession) -> JSONResponse:
    logger.info("[get_data_from_snap_api_handler][user(%s)] Fetching data from SNAP API", user)

    headers = {
        'Ocp-Apim-Subscription-Key': settings.SNAP_API_CLIENT_SECRET,
        'Accept': 'application/json'
    }
    user_id = user.get("sub")
    report_type = body.report_type
    request_id = ''
    await validate_access_to_report(db=db, user_id=user_id, report_type=report_type)

    request_payload = {report_type: body.report_input_value}

    async with httpx.AsyncClient() as client:
        response = await client.post(Endpoints.snap_report_enpoint + "/" + report_type, headers=headers, json=request_payload)

    if response.status_code==422:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Input value format wrong", user_id)
        raise HTTPException(**CustomErrorMessages.input_value_snap_wrong.to_dict())
    
    if response.status_code==500:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Problems with snap api", user_id)
        raise HTTPException(**CustomErrorMessages.problems_with_snap_api.to_dict())

    if response.status_code not in [200, 202]:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API failed", user_id)
        raise HTTPException(**CustomErrorMessages.snap_api_failed.to_dict(response.status_code))

    request_id = response.json().get('id')
    if not request_id:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API returned no ID", user_id)
        raise HTTPException(**CustomErrorMessages.snap_api_no_id.to_dict())

    return {"id": request_id}


async def insert_verifier_handler(body: InsertVerifier, db: AsyncSession, user: dict):
    logger.info("[insert_verifier_handler][user(%s)] Inserting verifier", user)
    user_id = user.get("sub")
    return await insert_verifier_on_db(db=db, user_id=user_id, verifier=body.verifier)


async def snap_status_ws(app: FastAPI, request_id: str, user_id: str, report_type: str,
                         report_number: str, report_search_args: dict, reports_id: str) -> None:
    """
    Track SNAP status for a report request.

    Args:
        app: FastAPI application instance
        request_id: SNAP API request identifier
        user_id: User identifier
        report_type: Type of report being processed
        report_number: Report number
        report_search_args: Search arguments for the report
        reports_id: Internal report identifier
    """
    tracker = SnapStatusTracker(app, user_id, reports_id)
    await tracker.track_status(request_id, report_type, report_number, report_search_args)









