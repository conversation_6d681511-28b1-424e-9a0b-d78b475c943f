import { useState, useEffect } from 'react'
import { MdImageNotSupported } from 'react-icons/md'
import { ImageWithFallback } from './ImageWithFallback'
import { isValidBase64Image } from '../../helpers'

interface ValidatedImageProps {
  src?: string | null
  alt?: string
  className?: string
  /**
   * While we’re pinging the URL, do you want:
   *  • nothing (default)?  
   *  • the broken placeholder?
   * 
   * If `false`, we skip placeholder until after we’ve checked.
   */
  showPlaceholderImmediately?: boolean
}

export function ValidatedImage({
  src,
  alt = 'Imagem',
  className = '',
  showPlaceholderImmediately = false,
}: ValidatedImageProps) {
  const [isImage, setIsImage] = useState<boolean | null>(
    Boolean(src && isValidBase64Image(src))
  )

  useEffect(() => {
    if (isImage === true || !src || isValidBase64Image(src)) {
      return
    }

    let cancelled = false
    const img = new Image()
    img.onload = () => {
      if (!cancelled) setIsImage(true)
    }
    img.onerror = () => {
      if (!cancelled) setIsImage(false)
    }
    img.src = src

    return () => {
      cancelled = true
    }
  }, [src])


  if (!src) return null


  if (isImage === null) {
    return showPlaceholderImmediately ? (
      <div className={`${className} flex items-center justify-center bg-neutral-100`}>
        <div className="animate-pulse bg-neutral-300 rounded-full w-12 h-12" />
      </div>
    ) : null
  }

  if (!isImage) {
    return (
      <div
        className={`
          ${className}
          flex items-center justify-center bg-neutral-10 text-neutral-300`}
      >
        <MdImageNotSupported className="w-12 h-12" />
      </div>
    )
  }

  return <ImageWithFallback src={src} alt={alt} className={className} />
}
