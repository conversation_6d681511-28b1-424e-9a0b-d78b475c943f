import json
import logging

from aiokafka import AIOKafkaConsumer
from fastapi import FastAPI
from fastapi.encoders import jsonable_encoder

from core.config import settings
from core.constants import SummaryReportStatus
from services.minio_service import load_from_minio

logger = logging.getLogger(__name__)

async def start_minio_event_consumer(app: FastAPI):
    logger.info("[start_minio_event_consumer] Starting MinIO Kafka Consumer...")
    logger.info(
        f"[start_minio_event_consumer] Setting Kafka consumer at"
        f" {settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT} or \n"
        f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}")

    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    consumer = AIOKafkaConsumer(
        "processed-reports",
        bootstrap_servers=kfk_servers,
        group_id="minio-ws-consumer"
    )
    await consumer.start()
    logger.info("[start_minio_event_consumer] MinIO Kafka Consumer started successfully.")

    try:
        async for msg in consumer:
            try:
                payload = json.loads(msg.value.decode())
                record = payload.get("Records", [{}])[0]
                key = record.get("s3", {}).get("object", {}).get("key")
                # key = payload['Records'][0]['s3']['object']['key']

                if not key:
                    logger.warning(f"[start_minio_event_consumer] No object key found in message: {payload}",)
                    continue

                logger.info("[start_minio_event_consumer] Received event for key: %s", key)

                # Assume file name is user_id_reports_id.json
                if "_" not in key:
                    logger.warning(f"[start_minio_event_consumer] Invalid key format: {key}")
                    continue

                user_id, reports_id = key.replace(".json", "").split("_", 1)

                manager = app.state.connection_manager
                websocket = manager.get_connection(user_id)

                if not websocket:
                    logger.warning(f"[start_minio_event_consumer] No active WebSocket found for user_id={user_id}."
                                   f" Skipping processing.")
                    continue  # Skip deleting the report

                logger.info(f"[start_minio_event_consumer] Found WebSocket for user_id={user_id}."
                            f"Report_id={reports_id}>Loading report...")

                try:
                    result = await load_from_minio(
                        bucket_name="processed-reports",
                        object_name=f"{user_id}_{reports_id}.json",
                        user_id=user_id
                    )
                except Exception as e:
                    logger.exception(f"[start_minio_event_consumer] Error loading report {user_id}_{reports_id}.json"
                                     f" from minio - {e}")
                    continue

                try:
                    safe_result = jsonable_encoder(result)
                    logger.info("[start_minio_event_consumer] Safe Result=%s", safe_result.keys())

                    message = {
                        "id": reports_id,
                        "status_code": SummaryReportStatus.success,
                        "result": safe_result,
                    }

                    try:
                        await websocket.send_json(message)
                        logger.info(f"[start_minio_event_consumer] Sent report to user_id={user_id}, "
                                    f"reports_id={reports_id}, status = {message['status_code']}")

                    except Exception as send_err:
                        logger.warning(f"[start_minio_event_consumer] Failed to send WebSocket message: {send_err}")
                        # manager.disconnect(user_id)
                        continue  # DO NOT delete the report


                    # try:
                    #     await delete_from_minio(
                    #         bucket_name="processed-reports",
                    #         object_name=f"{user_id}_{reports_id}.json",
                    #         user_id=user_id
                    #     )
                    # except Exception as delete_err:
                    #     logger.warning(f"[start_minio_event_consumer] Failed to delete from MinIO: {delete_err}")

                except RuntimeError as send_err:
                    logger.warning(
                        "[start_minio_event_consumer] Failed to send WebSocket message: %s. Cleaning up.",
                        send_err
                    )
                    # manager.disconnect(user_id)

            except Exception as e:
                logger.exception(f"[start_minio_event_consumer] Error processing Kafka message - {e}")

    finally:
        logger.info("[start_minio_event_consumer] Stopping MinIO Kafka Consumer...")
        await consumer.stop()

